#!/usr/bin/env python3
"""
测试Claude.ai长文本粘贴功能的Playwright脚本
"""

import asyncio
import time
from playwright.async_api import async_playwright

# 准备一个长文本用于测试
LONG_TEXT = """
这是一个用于测试Claude.ai长文本粘贴功能的示例文本。这个文本包含了多个段落和不同类型的内容，用来观察Claude.ai如何处理长文本的粘贴。

第一段：技术背景
在现代Web应用中，处理长文本输入是一个常见的挑战。用户可能需要粘贴大量的代码、文档内容或者其他长文本。一个好的文本输入组件需要能够：
1. 高效处理大量文本
2. 提供良好的用户体验
3. 避免浏览器性能问题
4. 支持各种文本格式

第二段：实现考虑
对于长文本处理，常见的技术方案包括：
- 虚拟滚动（Virtual Scrolling）
- 分块渲染（Chunked Rendering）
- 延迟加载（Lazy Loading）
- 文本压缩和优化

第三段：用户体验
从用户体验角度来看，长文本粘贴功能应该：
- 响应迅速，不出现明显延迟
- 保持文本格式
- 提供适当的视觉反馈
- 支持撤销和重做操作

第四段：技术实现细节
在技术实现上，可能涉及到：
- DOM操作优化
- 事件处理机制
- 内存管理
- 渲染性能优化

这个测试文本总共包含了大约1000个字符，足以测试基本的长文本处理能力。在实际应用中，用户可能会粘贴更长的内容，比如完整的代码文件、文档章节或者数据集。

代码示例：
```python
def process_long_text(text):
    # 处理长文本的示例函数
    chunks = []
    chunk_size = 1000
    
    for i in range(0, len(text), chunk_size):
        chunk = text[i:i + chunk_size]
        chunks.append(chunk)
    
    return chunks

# 使用示例
long_content = "这里是很长的文本内容..."
processed_chunks = process_long_text(long_content)
```

结论：
通过这个测试，我们可以观察Claude.ai如何处理长文本粘贴，包括性能表现、用户界面响应和整体用户体验。
""".strip()

async def test_claude_paste_functionality():
    """测试Claude.ai的长文本粘贴功能"""
    
    async with async_playwright() as p:
        # 启动浏览器
        browser = await p.chromium.launch(headless=False, slow_mo=1000)
        context = await browser.new_context()
        page = await context.new_page()
        
        try:
            print("正在访问Claude.ai...")
            await page.goto("https://claude.ai/new")
            
            # 等待页面加载
            await page.wait_for_load_state("networkidle")
            await asyncio.sleep(3)
            
            print("寻找输入框...")
            # 尝试多种可能的选择器来找到输入框
            input_selectors = [
                'textarea[placeholder*="Message"]',
                'textarea[placeholder*="message"]',
                'textarea[data-testid*="input"]',
                'textarea[role="textbox"]',
                'div[contenteditable="true"]',
                '[data-testid="chat-input"]',
                '.ProseMirror',
                'textarea',
                '[contenteditable="true"]'
            ]
            
            input_element = None
            for selector in input_selectors:
                try:
                    input_element = await page.wait_for_selector(selector, timeout=5000)
                    if input_element:
                        print(f"找到输入框，使用选择器: {selector}")
                        break
                except:
                    continue
            
            if not input_element:
                print("未找到输入框，尝试点击页面中央激活输入")
                await page.click("body")
                await asyncio.sleep(1)
                
                # 再次尝试寻找输入框
                for selector in input_selectors:
                    try:
                        input_element = await page.wait_for_selector(selector, timeout=3000)
                        if input_element:
                            print(f"激活后找到输入框，使用选择器: {selector}")
                            break
                    except:
                        continue
            
            if input_element:
                print("开始测试长文本粘贴...")
                
                # 点击输入框确保焦点
                await input_element.click()
                await asyncio.sleep(1)
                
                # 记录开始时间
                start_time = time.time()
                
                # 模拟粘贴操作
                print(f"粘贴文本长度: {len(LONG_TEXT)} 字符")
                
                # 方法1: 使用fill方法
                try:
                    await input_element.fill(LONG_TEXT)
                    print("使用fill方法成功")
                except Exception as e:
                    print(f"fill方法失败: {e}")
                    
                    # 方法2: 使用type方法
                    try:
                        await input_element.type(LONG_TEXT)
                        print("使用type方法成功")
                    except Exception as e:
                        print(f"type方法失败: {e}")
                        
                        # 方法3: 使用剪贴板
                        try:
                            await page.evaluate(f"""
                                navigator.clipboard.writeText(`{LONG_TEXT}`);
                            """)
                            await page.keyboard.press("Control+V")
                            print("使用剪贴板粘贴成功")
                        except Exception as e:
                            print(f"剪贴板方法失败: {e}")
                
                # 记录结束时间
                end_time = time.time()
                paste_duration = end_time - start_time
                
                print(f"粘贴操作耗时: {paste_duration:.2f} 秒")
                
                # 等待一下让界面更新
                await asyncio.sleep(2)
                
                # 分析页面性能和DOM结构
                print("分析页面结构...")
                
                # 获取输入框的属性和样式
                input_info = await input_element.evaluate("""
                    element => {
                        return {
                            tagName: element.tagName,
                            type: element.type,
                            className: element.className,
                            id: element.id,
                            placeholder: element.placeholder,
                            value: element.value ? element.value.length : 0,
                            textContent: element.textContent ? element.textContent.length : 0,
                            contentEditable: element.contentEditable,
                            scrollHeight: element.scrollHeight,
                            clientHeight: element.clientHeight,
                            style: {
                                height: element.style.height,
                                maxHeight: element.style.maxHeight,
                                overflow: element.style.overflow
                            }
                        };
                    }
                """)
                
                print("输入框信息:")
                for key, value in input_info.items():
                    print(f"  {key}: {value}")
                
                # 检查是否有虚拟滚动或其他优化
                virtual_scroll_info = await page.evaluate("""
                    () => {
                        const elements = document.querySelectorAll('[class*="virtual"], [class*="scroll"], [class*="lazy"]');
                        return Array.from(elements).map(el => ({
                            tagName: el.tagName,
                            className: el.className,
                            id: el.id
                        }));
                    }
                """)
                
                if virtual_scroll_info:
                    print("发现可能的虚拟滚动元素:")
                    for info in virtual_scroll_info:
                        print(f"  {info}")
                
                # 检查性能指标
                performance_metrics = await page.evaluate("""
                    () => {
                        const navigation = performance.getEntriesByType('navigation')[0];
                        return {
                            domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
                            loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
                            memoryUsage: performance.memory ? {
                                used: performance.memory.usedJSHeapSize,
                                total: performance.memory.totalJSHeapSize,
                                limit: performance.memory.jsHeapSizeLimit
                            } : null
                        };
                    }
                """)
                
                print("性能指标:")
                for key, value in performance_metrics.items():
                    print(f"  {key}: {value}")
                
                # 等待用户观察
                print("等待10秒供观察...")
                await asyncio.sleep(10)
                
            else:
                print("无法找到输入框")
                # 截图保存当前页面状态
                await page.screenshot(path="claude_page_screenshot.png")
                print("已保存页面截图: claude_page_screenshot.png")
                
        except Exception as e:
            print(f"测试过程中出现错误: {e}")
            await page.screenshot(path="error_screenshot.png")
            
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(test_claude_paste_functionality())
